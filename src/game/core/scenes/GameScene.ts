import { CLOUD_STORAGE_KEYS } from '@/constants'
import { EnemyStatsHandler } from '@/game/core/CoreGameplay/Achievements/EnemyStatsHandler.ts'
import { achievements } from '@/game/core/CoreGameplay/Achievements/GameplayAchievementsHandler.ts'
import { BackgroundHandler } from '@/game/core/CoreGameplay/BackgroundHandler'
import { CameraManager } from '@/game/core/CoreGameplay/CameraManager'
import { AtlasNames, GameViewConsts } from '@/game/core/CoreGameplay/Constants/GameViewConsts.ts'
import { GameBuilder } from '@/game/core/CoreGameplay/GameBuilder'
import GameContainer from '@/game/core/CoreGameplay/GameContainer'
import { GameInterface } from '@/game/core/CoreGameplay/GameInterface'
import {
  GameSequenceController,
  SequenceType
} from '@/game/core/CoreGameplay/GameSequences/GameSequenceController'
import { gameplayAssetsPreloader } from '@/game/core/CoreGameplay/GameplayAssetsPreloader.ts'
import { PlatformsDebugViewController } from '@/game/core/CoreGameplay/Platforms/PlatformsDebugViewController'
import { PlayerManager } from '@/game/core/CoreGameplay/Player/PlayerManager.ts'
import type { SessionProperties } from '@/game/core/MapGen/GameSessionManager.ts'
import type { StackableBoosterType } from '@/services/openapi'
import { Logger, logger } from '@/shared/Logger'
import { UI_DEBUG_EVENTS } from '@/shared/constants/debug/uiDebugEvents'
import { GAME_EVENTS, UI_EVENTS } from '@/shared/constants/uiEvents'
import { boostersCache } from '@/shared/storage/cachedBoosters.ts'
import { cachedPlayerState } from '@/shared/storage/cachedPlayerState.ts'
import { cloudStorageService } from '@/shared/storage/cloudStorageService'
import type { EventBus } from '@/shared/types'
import * as Sentry from '@sentry/vue'

import WebGLRenderer = Phaser.Renderer.WebGL.WebGLRenderer

export const boosterTypeToEnumValue: Record<StackableBoosterType, number> = {
  stackableMagneticField: 0,
  stackableJumper: 1,
  stackableAimbot: 2
}

function mapBoosterTypesToNumbers(boosters: StackableBoosterType[]): number[] {
  return boosters.map(type => boosterTypeToEnumValue[type])
}

export class GameScene extends Phaser.Scene {
  private gameBuilder!: GameBuilder
  private playerManager!: PlayerManager
  private cameraManager!: CameraManager
  private background!: BackgroundHandler
  private platformsDebugView!: PlatformsDebugViewController
  private isSceneReady: boolean = false
  private isGameEnded: boolean = false
  private readonly uiEventBus: EventBus
  private gameSequence!: GameSequenceController
  private logger: Logger
  private gameInterface!: GameInterface
  private fpsText!: Phaser.GameObjects.Text
  private fpsUpdateInterval: number = 500
  private fpsLastUpdateTime: number = 0
  private debugCollidersEnabled!: boolean
  private boostedFromRestart: boolean = false
  private enemyStats: EnemyStatsHandler | null = null
  private isGyroscopeControlMethod: boolean = false

  public get GameBuilder(): GameBuilder {
    return this.gameBuilder
  }

  public get UiEventBus(): EventBus {
    return this.uiEventBus
  }

  public get EnemyStats(): EnemyStatsHandler {
    return this.enemyStats!
  }

  public get IsGyroscopeControlMethod(): boolean {
    console.log('isGyroscopeControlMethod: ' + this.isGyroscopeControlMethod)
    return this.isGyroscopeControlMethod
  }

  constructor(uiEventBus: EventBus) {
    super({ key: GameScene.name })
    this.uiEventBus = uiEventBus
    this.logger = logger

    if (__DEV__) this.logger.info('Scene created')
  }

  private async initializeControlMethod(): Promise<void> {
    try {
      const saved = await cloudStorageService.load<boolean>(
        CLOUD_STORAGE_KEYS.isControlMethodGyroscope
      )
      console.log('isGyroscopeControlMethod saved' + saved)
      this.isGyroscopeControlMethod = saved ?? false
    } catch (error) {
      if (__DEV__) this.logger.warn('Failed to load control method from cloud storage:', error)
      this.isGyroscopeControlMethod = false
    }
  }

  private async toggleGyroEnabled(): Promise<void> {
    try {
      const current = this.isGyroscopeControlMethod
      const next = !current
      await cloudStorageService.save(CLOUD_STORAGE_KEYS.isControlMethodGyroscope, next)
      this.isGyroscopeControlMethod = next
    } catch (error) {
      console.warn('Failed to toggle gyro setting:', error)
    }
  }

  async preload(): Promise<void> {
    if (
      cachedPlayerState.playerState?.tutorial === true ||
      cachedPlayerState.playerState?.tutorialMobs === true ||
      cachedPlayerState.playerState?.leagueLevel! <= 2
    ) {
      await gameplayAssetsPreloader.preloadFTUEAssets(this)
    }
    this.createStartersAnimation()
    this.createPlatformAnimations()
    this.createBoosterAnimations()
  }

  createStartersAnimation(): void {
    this.anims.create({
      key: 'bootsAnim',
      frames: [
        { key: AtlasNames.EFFECTS, frame: 'Jumpers_effect-02.png' },
        { key: AtlasNames.EFFECTS, frame: 'Jumpers_effect-01.png' }
      ],
      frameRate: 8,
      repeat: 0
    })
  }

  private createPlatformAnimations() {
    this.anims.create({
      key: 'crashAnim',
      frames: [
        { key: AtlasNames.ANIM, frame: 'Platform_Crash-01a.png' },
        { key: AtlasNames.ANIM, frame: 'Platform_Crash-01b.png' },
        { key: AtlasNames.ANIM, frame: 'Platform_Crash-01c.png' },
        { key: AtlasNames.ANIM, frame: 'Platform_Crash-01d.png' }
      ],
      frameRate: 48,
      repeat: 0
    })

    this.anims.create({
      key: 'bubbleAnim',
      frames: [
        { key: AtlasNames.ANIM, frame: 'bubble_4.png' },
        { key: AtlasNames.ANIM, frame: 'bubble_5.png' }
      ],
      frameRate: 20,
      repeat: 0
    })
  }

  private createBoosterAnimations() {
    this.anims.create({
      key: 'arrowAnim',
      frames: this.anims.generateFrameNames('boosterAnims', {
        prefix: 'arrow_',
        start: 1,
        end: 6,
        suffix: '.png'
      }),
      frameRate: 48,
      repeat: 0
    })

    this.anims.create({
      key: 'doubleArrowAnim',
      frames: this.anims.generateFrameNames('boosterAnims', {
        prefix: 'double_arrow_',
        start: 1,
        end: 7,
        suffix: '.png'
      }),
      frameRate: 48,
      repeat: 0
    })
  }

  async init(): Promise<void> {
    Sentry.addBreadcrumb({
      message: 'Game scene initialization started',
      category: 'game',
      level: 'info'
    })

    const gameContainer = GameContainer.initialize(this)
    this.gameSequence = gameContainer.resolve<GameSequenceController>(GameSequenceController)
    this.cameraManager = gameContainer.resolve<CameraManager>(CameraManager)
    this.gameBuilder = gameContainer.resolve<GameBuilder>(GameBuilder)
    this.gameInterface = gameContainer.resolve<GameInterface>(GameInterface)
    this.enemyStats = new EnemyStatsHandler(this)
    if (__DEV__) console.log('Scene initialized')
  }

  async create(): Promise<void> {
    try {
      this.initializeSceneProperties()
      this.setupCamera()
      await this.initializeBackgroundAndGameInterface()
      this.initializeDebugView()
      this.createPlayerAndCamera()

      this.setupEventListeners()
      this.displayFPS()
      achievements.initAchievements()

      Sentry.addBreadcrumb({
        message: 'Game scene created successfully',
        category: 'game',
        level: 'info',
        data: {
          WebGL: isWebGLSupported(),
          WebGL2: isWebGL2Supported(),
          MaxTextureSize: getMaxTextureSize(),
          MaxRenderbufferSize: getMaxRenderbufferSize()
        }
      })

      if (__DEV__) this.toggleDebug()
    } catch (error: any) {
      this.shutdown()
      this.logger.error('Error during scene creation:', error)
      Sentry.captureException(error, {
        tags: {
          section: 'game-scene-creation'
        },
        extra: {
          sceneReady: this.isSceneReady,
          gameEnded: this.isGameEnded
        }
      })
    }

    this.uiEventBus.emit(GAME_EVENTS.GAME_PRELOAD_ENDED)
    this.isSceneReady = true
    console.log('Game scene created')
  }

  private initializeSceneProperties(): void {
    const parent = window.parent
    if (parent && this.scale && typeof this.scale.resize === 'function') {
      const width = parent.outerWidth
      const height = parent.outerHeight
      this.scale.resize(width, height)
    } else {
      console.warn('GameScene.initializeSceneProperties: scale object is not available.')
    }

    this.fpsLastUpdateTime = 0
    this.isSceneReady = false
    this.isGameEnded = false
  }

  private async initializeBackgroundAndGameInterface(): Promise<void> {
    if (__DEV__) this.logger.info('Initializing game interface...')
    try {
      await this.initializeControlMethod()
    } catch (e) {
      console.error('Failed to initialize control method:', e)
    }

    let hasTutorial = cachedPlayerState?.playerState?.tutorial ?? false
    const playerLeague = cachedPlayerState?.playerState?.leagueLevel
    if (__DEV__) {
      hasTutorial = (!this.boostedFromRestart && cachedPlayerState.playerState?.tutorial) || false
    }

    const hasMobsTutorial = cachedPlayerState?.playerState?.tutorialMobs ?? false

    const activeBoosters = boostersCache?.active || []
    const boosters = mapBoosterTypesToNumbers(activeBoosters)
    const controlMode = this.isGyroscopeControlMethod ? 0 : 1
    await this.gameInterface.initGame(
      hasTutorial,
      hasMobsTutorial,
      playerLeague,
      boosters,
      controlMode
    )
  }

  private setupCamera(): void {
    if (!this.sys || !this.sys.game || !this.sys.game.config) {
      console.warn('setupCamera: Scene systems not fully initialized.')
      return
    }

    const newWidth = this.sys.game.config.width as number
    const newHeight = this.sys.game.config.height as number
    const scaleX = newWidth / GameViewConsts.REF_WIDTH
    const scaleY = newHeight / GameViewConsts.REF_HEIGHT
    const finalScale = Math.min(scaleX, scaleY)
    this.cameras.main.setZoom(finalScale)
    this.cameras.main.centerOn(GameViewConsts.REF_WIDTH / 2, GameViewConsts.REF_HEIGHT / 2)
    this.lights.disable()
  }

  private createBackground(): void {
    this.background = new BackgroundHandler(this)
    this.background.createRandomBackground()
  }

  private initializeDebugView(): void {
    this.platformsDebugView = new PlatformsDebugViewController()
    this.platformsDebugView.setPlatformGroup(this.gameBuilder.levelBuilder.Interactables.Platforms)
  }

  private createPlayerAndCamera(): void {
    this.playerManager = new PlayerManager(
      this,
      this.gameBuilder.levelBuilder.Interactables,
      this.uiEventBus,
      this.cameraManager
    )

    const playerInstance = this.playerManager.createPlayer(
      this.gameBuilder.levelBuilder.firstPlatform,
      this.gameInterface.currentMultiplier
    )
    this.cameraManager.createCamera(playerInstance)
  }

  private setupEventListeners(): void {
    this.setupGameEventListeners()
  }

  private setupGameEventListeners(): void {
    this.uiEventBus.on(
      UI_DEBUG_EVENTS.CHANGE_PLATFORMS,
      this.platformsDebugView.trySwitchPlatformsView.bind(this.platformsDebugView)
    )
    this.uiEventBus.on(UI_DEBUG_EVENTS.TOGGLE_CONTROLS, this.toggleGyroEnabled.bind(this))

    this.uiEventBus.on(UI_EVENTS.GAME_OVER, this.sendEndGame.bind(this))
    this.uiEventBus.on(
      UI_DEBUG_EVENTS.CHANGE_CHARACTER,
      this.playerManager.cheatUni.bind(this.playerManager)
    )
    this.uiEventBus.on(
      UI_DEBUG_EVENTS.TOGGLE_BOOTS,
      this.playerManager.toggleBoots.bind(this.playerManager)
    )

    this.uiEventBus.on(
      UI_DEBUG_EVENTS.TOGGLE_AIM,
      this.playerManager.toggleAim.bind(this.playerManager)
    )

    this.uiEventBus.on(UI_DEBUG_EVENTS.CHANGE_TICKETS, this.changeTickets.bind(this))
    if (__DEV__) this.uiEventBus.on(UI_DEBUG_EVENTS.TOGGLE_DEBUG, this.toggleDebug.bind(this))

    //if (__DEV__) this.uiEventBus.on(UI_DEBUG_EVENTS.CHANGE_BACKGROUND, this.background.cheatBack.bind(this))

    this.events.on('current-score-changed', this.handleScoreChanged, this)
    this.uiEventBus.on(UI_EVENTS.GAME_CONTINUED, this.continueFromLastPosition.bind(this))
  }

  sendEndGame() {
    const currentScore = this.getCurrentScore()
    this.sendGameEnded(currentScore)
  }

  private toggleDebug(): void {
    if (__DEV__) return
    this.debugCollidersEnabled = !this.debugCollidersEnabled
    if (!this.debugCollidersEnabled) {
      this.physics.world.debugGraphic.clear()
    }
    this.physics.world.drawDebug = this.debugCollidersEnabled
    if (this.debugCollidersEnabled) {
      if (__DEV__) this.logger.info('Debug colliders enabled')
    } else {
      this.physics.world.debugGraphic.clear()
      this.physics.world.drawDebug = false
    }
  }

  changeTickets() {
    this.gameBuilder.levelBuilder.Interactables.Collectables.forEach(c => c.changeVisuals())
  }

  private displayFPS(): void {
    const MAX_DEPTH = 999999
    const FPS_FONT_SIZE = '16px'
    const FPS_TEXT_COLOR = '#000000'

    if (__DEV__) {
      this.fpsText = this.add
        .text(-window.outerWidth / 3, -50, 'FPS: ', {
          fontSize: FPS_FONT_SIZE,
          color: FPS_TEXT_COLOR
        })
        .setDepth(MAX_DEPTH)
        .setScrollFactor(0)
    }
  }

  private handleScoreChanged(score: number): void {
    this.gameBuilder.onScoreUpdated(score)
  }

  setRestart() {
    this.boostedFromRestart = true
  }

  private pauseGame(): void {
    this.scene.pause()
  }

  private resumeGame(): void {
    this.scene.resume()
  }

  private checkOffscreenMobs(): void {
    const offscreenStartingPoint =
      this.cameras.main.worldView.y + this.cameras.main.worldView.height
    this.gameBuilder.levelBuilder?.checkOffscreenMobs(offscreenStartingPoint)
  }

  update(time: number, delta: number): void {
    if (this.playerManager) {
      this.playerManager.updatePlayer(time, delta)
    }

    this.checkOffscreenMobs()

    if (this.isSceneReady && !this.isGameEnded) {
      const currentTime = Date.now()

      if (currentTime - this.fpsLastUpdateTime > this.fpsUpdateInterval) {
        const deltaFps = 1000 / this.game.loop.delta
        const actualFps = this.game.loop.actualFps
        if (__DEV__) {
          this.fpsText.setText(
            `delta FPS: ${deltaFps.toFixed(3)} \nactual FPS: ${actualFps.toFixed(3)}`
          )
        }

        if (deltaFps < 30) {
          Sentry.addBreadcrumb({
            message: 'Low FPS detected',
            category: 'performance',
            level: 'warning',
            data: {
              fps: deltaFps.toFixed(0),
              score: this.getCurrentScore(),
              session: this.getActiveSessionInfo()?.id
            }
          })
        }

        this.fpsLastUpdateTime = currentTime
      }
    }
  }

  public continueFromLastPosition(): void {
    this.isGameEnded = false
    this.gameSequence.switchSequence(SequenceType.ContinueGame)
    this.gameInterface.clearCachedInteractions()
  }

  public invokeEndGame(regularDeath: boolean): void {
    try {
      this.uiEventBus.emit(GAME_EVENTS.PLAYER_BELOW_SCREEN_BOTTOM)
      this.isGameEnded = true
      if (!regularDeath) return
      this.gameSequence.switchSequence(SequenceType.GameOver)
    } catch (error: unknown) {
      if (__DEV__) this.logger.error('Error invoking end game:', error)
    }
  }

  async setupAfterReviving(lastPlayerPos: number): Promise<number> {
    return await this.gameBuilder.levelBuilder.setupAfterReviving(lastPlayerPos)
  }

  getPlayer(): Phaser.Physics.Arcade.Sprite | null {
    const player = this.playerManager?.playerController?.getPlayer()
    if (!player) {
      return null
    }
    return player
  }

  getCurrentScore(): number {
    const currentScore = this.playerManager?.playerController?.getCurrentScore() || 0
    return parseInt(currentScore.toFixed(0))
  }

  private sendGameEnded(score: number): void {
    this.gameInterface.registerGameEnd(score)
  }

  getActiveSessionInfo(): SessionProperties | undefined {
    return this.gameInterface.getActiveSessionInfo()
  }

  shutdown(): void {
    this.uiEventBus.offAll(UI_EVENTS.GAME_CONTINUED)
    this.uiEventBus.offAll(UI_DEBUG_EVENTS.TOGGLE_DEBUG)
    this.uiEventBus.offAll(UI_DEBUG_EVENTS.TOGGLE_CONTROLS)

    // if (this.background) {
    //   this.uiEventBus.offAll(UI_DEBUG_EVENTS.CHANGE_PLATFORMS)
    // }

    if (this.playerManager) {
      this.uiEventBus.offAll(UI_DEBUG_EVENTS.CHANGE_CHARACTER)
    }

    if (this.platformsDebugView) {
      this.uiEventBus.offAll(UI_DEBUG_EVENTS.CHANGE_TICKETS)
    }

    if (this.gameInterface) {
      this.UiEventBus.offAll(UI_EVENTS.GAME_OVER)
    }

    this.uiEventBus.offAll(UI_DEBUG_EVENTS.TOGGLE_BOOTS)
    this.uiEventBus.offAll(UI_DEBUG_EVENTS.TOGGLE_AIM)

    this.events.off('current-score-changed', this.handleScoreChanged, this)
    this.playerManager?.playerController?.unsubscribe()

    if (this.gameBuilder && this.gameBuilder.levelBuilder) {
      this.gameBuilder?.levelBuilder?.clearAllPools()
      console.warn('GameScene.shutdown: all events unsubbed and objects cleared')
    } else {
      console.warn('GameScene.shutdown: gameBuilder or levelBuilder is not defined.')
    }

    this.tweens.killAll()
  }

  private cleanupInProgress = false
  async safeRestart() {
    if (this.cleanupInProgress) return // Prevent multiple cleanups
    this.cleanupInProgress = true

    // Perform async cleanup
    await this.cleanupResources()
    const gl = (this.renderer as WebGLRenderer).gl
    if (gl) {
      gl.bindBuffer(gl.ARRAY_BUFFER, null)
      gl.bindBuffer(gl.ELEMENT_ARRAY_BUFFER, null)
      gl.bindFramebuffer(gl.FRAMEBUFFER, null)
      console.log('--- WebGL Limits ---')
      console.log('Max Combined Textures:', gl.getParameter(gl.MAX_COMBINED_TEXTURE_IMAGE_UNITS))
      console.log('Max Vertex Textures:', gl.getParameter(gl.MAX_VERTEX_TEXTURE_IMAGE_UNITS))
      console.log('Max Texture Size:', gl.getParameter(gl.MAX_TEXTURE_SIZE))
      console.log('Max Renderbuffer Size:', gl.getParameter(gl.MAX_RENDERBUFFER_SIZE))
      console.log('Max Vertex Uniform Vectors:', gl.getParameter(gl.MAX_VERTEX_UNIFORM_VECTORS))
      console.log('Max Fragment Uniform Vectors:', gl.getParameter(gl.MAX_FRAGMENT_UNIFORM_VECTORS))
      console.log('Max Viewport Dimensions:', gl.getParameter(gl.MAX_VIEWPORT_DIMS))
      console.log('Max Textures in Batch:', (this.renderer as WebGLRenderer).maxTextures)

      if (gl instanceof WebGL2RenderingContext) {
        console.log('Max Draw Buffers:', gl.getParameter(gl.MAX_DRAW_BUFFERS))
      } else {
        console.warn('WebGL2 not supported. Cannot query MAX_DRAW_BUFFERS.')
      }
      const maxViewport = gl.getParameter(gl.MAX_VIEWPORT_DIMS)
      console.log(`Max Viewport Dimensions: ${maxViewport[0]}x${maxViewport[1]}`)

      if (this.renderer instanceof Phaser.Renderer.WebGL.WebGLRenderer) {
        const drawCount = (this.renderer as any).drawCount // Access drawCount
        console.log('Draw Calls This Frame:', drawCount)
      } else {
        console.warn('Renderer is not WebGL.')
      }

      console.log('Max Combined Textures:', gl.getParameter(gl.MAX_COMBINED_TEXTURE_IMAGE_UNITS))
      console.log('Max Texture Size:', gl.getParameter(gl.MAX_TEXTURE_SIZE))
    } else {
      console.warn('Renderer is not WebGL. Cannot query WebGL limits.')
    }

    const textures = this.textures.list
    let totalMemory: number = 0
    // Check textures
    Object.entries(textures).forEach(([key, texture]) => {
      const source = texture.getSourceImage()
      if (source) {
        const width = source.width
        const height = source.height
        if (typeof width === 'number' && typeof height === 'number' && width > 0 && height > 0) {
          // Assuming RGBA (4 bytes per pixel)
          const memory = width * height * 4

          console.log(
            `Texture: ${key}, Dimensions: ${width}x${height}, Memory: ${(memory / (1024 * 1024)).toFixed(2)} MB`
          )
          totalMemory += memory
        } else {
          console.warn(`Invalid texture dimensions for key "${key}". Skipping.`)
        }
      } else {
        console.warn(`Texture "${key}" has no source image. Skipping.`)
      }
    })
    console.log(`Total Texture Memory Used: ${(totalMemory / (1024 * 1024)).toFixed(2)} MB`)

    //gl?.getExtension('WEBGL_lose_context')?.loseContext();
    console.log('Active WebGL Contexts:', document.querySelectorAll('canvas').length)

    // Restart the scene
    this.scene.stop(GameScene.name)
    this.scene.start(GameScene.name)
  }

  destroy(): void {
    console.log('Destroying game scene')
    try {
      //this.input.manager.destroy(); // Destroy input events
      this.cameras.main.destroy()
      this.time.removeAllEvents()
      super.spine.destroy()
      this.children.each(child => child.destroy()) // Destroy all game objects
    } catch (e) {
      console.error('Error destroying game scene:', e)
    }
  }

  async cleanupResources() {
    console.log('Starting cleanup...')

    // Example: Async destroy objects
    const destroyPromises: any[] = []
    this.children.each(child => {
      destroyPromises.push(
        new Promise<void>(resolve => {
          child.destroy()
          resolve()
        })
      )
    })

    // Wait for all destroy operations
    await Promise.all(destroyPromises)

    // Clear timers and events
    this.time.removeAllEvents()
    this.events.off('event-name')
    this.game.events.off('event-name')

    console.log('Cleanup complete')
    this.cleanupInProgress = false
  }
}

function isWebGLSupported(): boolean {
  try {
    const canvas = document.createElement('canvas')
    return !!(
      window.WebGLRenderingContext &&
      (canvas.getContext('webgl') || canvas.getContext('experimental-webgl'))
    )
  } catch (e) {
    return false
  }
}

function isWebGL2Supported(): boolean {
  try {
    const canvas = document.createElement('canvas')
    return !!canvas.getContext('webgl2')
  } catch (e) {
    return false
  }
}

function getMaxTextureSize(): number | null {
  const canvas = document.createElement('canvas')
  const gl =
    (canvas.getContext('webgl') as WebGLRenderingContext | null) ||
    (canvas.getContext('experimental-webgl') as WebGLRenderingContext | null)
  if (gl) {
    const size = gl.getParameter(gl.MAX_TEXTURE_SIZE)
    const ext = gl.getExtension('WEBGL_lose_context')
    if (ext) ext.loseContext()
    return size
  }
  console.warn('WebGL is not supported.')
  return null
}

function getMaxRenderbufferSize(): number | null {
  const canvas = document.createElement('canvas')
  const gl =
    (canvas.getContext('webgl2') as WebGL2RenderingContext | null) ||
    (canvas.getContext('webgl') as WebGLRenderingContext | null)
  if (gl) {
    const size = gl.getParameter(gl.MAX_RENDERBUFFER_SIZE)
    const ext = gl.getExtension('WEBGL_lose_context')
    if (ext) ext.loseContext()
    return size
  }
  console.warn('WebGL is not supported.')
  return null
}
export function delay(ms: number): Promise<void> {
  return new Promise(resolve => setTimeout(resolve, ms))
}
