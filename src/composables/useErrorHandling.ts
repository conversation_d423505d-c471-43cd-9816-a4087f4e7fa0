import { hapticsService } from '@/shared/haptics/hapticsService.ts'
import { useToast } from '@/stores/toastStore'
import * as Sentry from '@sentry/vue'

const ERROR_MESSAGES: Record<string, string> = {
  RATE_LIMIT: 'Request limit exceeded.\nPlease try again later.'
}

const PURCHASE_ERROR_MESSAGE: Record<string, string> = {
  NOT_ENOUGH_HARD_CURRENCY: 'Not enough funds'
}

const FARMING_ERROR_MESSAGE: Record<string, string> = {
  FARMING_IN_PROGRESS: 'Farming in progress.'
}

const REFERRAL_REWARD_ERROR_MESSAGES: Record<string, string> = {
  RATE_LIMIT: 'Try to grab the reward\nagain in 5 minutes.'
}

const WALLET_ERROR_MESSAGES: Record<string, string> = {
  TESTNET_NOT_ALLOWED: 'Testnet wallets not allowed.',
  BOUNCEABLE_NOT_ALLOWED: 'Bounceable address type not allowed.'
}

const SKINS_ERROR_MESSAGES: Record<string, string> = {
  INSUFFICIENT_BALANCE: 'Insufficient balance',
  SKIN_LOCKED: 'Skin locked',
  PURCHASED_ALREADY: 'Purchased already',
  TRANSACTION_FAILED: 'Transaction failed',
  NOT_FOUND: 'Skin not found'
}

const REVIVE_ERROR_MESSAGES: Record<string, string> = {
  SESSION_NOT_FOUND: 'Cant provide revive info [session not found]',
  PURCHASE_FAILED: 'Purchase failed',
  NOT_ALLOWED: 'No more revives left'
}

const START_CLAN_EVENT_ERROR_MESSAGES: Record<string, string> = {
  NOT_ENOUGH_MEMBERS_FOR_CLAN_EVENT: 'Clan should have more than 1 member to start the event'
}

const displayErrorMessage = (message: string) => {
  const { showToast } = useToast()
  showToast(message, 'warning')
  console.error(message)

  Sentry.addBreadcrumb({
    message: 'User error displayed',
    category: 'user-error',
    level: 'warning',
    data: { message }
  })
}

const useDefaultErrorMessage = (showMessage: (message: string) => void) => (errorCode: string) => {
  if (ERROR_MESSAGES[errorCode]) {
    showMessage(ERROR_MESSAGES[errorCode])
  } else {
    showMessage(
      'Something went wrong, please contact support' + (errorCode ? ` (${errorCode})` : '')
    )
  }

  Sentry.captureMessage(`API Error: ${errorCode}`, {
    level: 'error',
    tags: {
      errorType: 'api-error',
      errorCode
    }
  })
}

const useErrorMessage =
  (showMessage: (message: string) => void) =>
  (messagesMap: Record<string, string>) =>
  (errorCode: string) => {
    hapticsService.triggerNotificationHapticEvent('warning')
    if (messagesMap[errorCode]) {
      showMessage(messagesMap[errorCode])
    } else if (ERROR_MESSAGES[errorCode]) {
      showMessage(ERROR_MESSAGES[errorCode])
    } else {
      showMessage(errorCode)
    }
  }

export const useDefaultErrorHandler = useDefaultErrorMessage(displayErrorMessage)
export const usePurchaseErrorHandling = useErrorMessage(displayErrorMessage)(PURCHASE_ERROR_MESSAGE)
export const useFarmingErrorHandling = useErrorMessage(displayErrorMessage)(FARMING_ERROR_MESSAGE)
export const useWalletErrorHandling = useErrorMessage(displayErrorMessage)(WALLET_ERROR_MESSAGES)
export const useSkinsErrorHandling = useErrorMessage(displayErrorMessage)(SKINS_ERROR_MESSAGES)
export const useReviveErrorHandling = useErrorMessage(displayErrorMessage)(REVIVE_ERROR_MESSAGES)
export const useReferralRewardErrorHandling = useErrorMessage(displayErrorMessage)(
  REFERRAL_REWARD_ERROR_MESSAGES
)
export const useStartClanEventErrorHandling = useErrorMessage(displayErrorMessage)(
  START_CLAN_EVENT_ERROR_MESSAGES
)
